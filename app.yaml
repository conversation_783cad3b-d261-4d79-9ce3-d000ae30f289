runtime: go122
service: default

# Environment variables
env_variables:
  PORT: "8080"
  PROXY_URLS: "http://sp0o8xf1er:<EMAIL>:10001,http://sp0o8xf1er:<EMAIL>:10002,http://sp0o8xf1er:<EMAIL>:10003,http://sp0o8xf1er:<EMAIL>:10004,http://sp0o8xf1er:<EMAIL>:10005,http://sp0o8xf1er:<EMAIL>:10006,http://sp0o8xf1er:<EMAIL>:10007,http://sp0o8xf1er:<EMAIL>:10008,http://sp0o8xf1er:<EMAIL>:10009,http://sp0o8xf1er:<EMAIL>:10010"

# Automatic scaling configuration
automatic_scaling:
  min_instances: 1
  max_instances: 10
  target_cpu_utilization: 0.6

# Resource allocation
resources:
  cpu: 1
  memory_gb: 0.5

handlers:
- url: /.*
  script: auto

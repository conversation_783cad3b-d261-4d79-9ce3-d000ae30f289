package main

import (
    "encoding/json"
    "fmt"
    "log"
    "net/http"
    "net/url"
    "os"
    "path/filepath"
    "strconv"
    "time"
    "propbolt/zestimate"
    "propbolt/details"
    "propbolt/config"
    "propbolt/utils"
)

/* internal notes
lsof -i :8080
kill -9
git add .
git commit -m ""
git push origin main
Production: GOOS=linux GOARCH=amd64 go build -o propbolt
Local: go build -o propbolt
Local: PORT=8080 ./propbolt
Local with proxies: PORT=8080 PROXY_URLS="http://proxy1:port,http://proxy2:port" ./propbolt
*/

// Global proxy configuration
var proxyConfig *config.ProxyConfig

// Global proxy rotator for better sharing across requests
var globalProxyRotator *utils.ProxyRotator

// Middleware for basic request logging (RapidAPI authentication removed)
func requestLoggingMiddleware(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        // Log the request for debugging purposes
        log.Printf("Request: %s %s from %s", r.<PERSON>, r.URL.Path, r.RemoteAddr)

        // All endpoints are now publicly accessible
        next.ServeHTTP(w, r)
    })
}

// Handler for the health check endpoint
func healthCheckHandler(w http.ResponseWriter, r *http.Request) {
    w.Header().Set("Content-Type", "application/json")
    w.WriteHeader(http.StatusOK)
    json.NewEncoder(w).Encode(map[string]string{"status": "ok"})
}

// Handler for serving static files
func serveStaticFile(filePath string) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        http.ServeFile(w, r, filepath.Join("public", filePath))
    }
}

// Handler for serving the favicon
func faviconHandler(w http.ResponseWriter, r *http.Request) {
    http.ServeFile(w, r, filepath.Join("public", "favicon.ico"))
}

// New handler for the /property endpoint
func propertyHandler(w http.ResponseWriter, r *http.Request) {
    // Parse the listingPhotos parameter
    listingPhotosStr := r.URL.Query().Get("listingPhotos")
    listingPhotos := false
    if listingPhotosStr == "true" {
        listingPhotos = true
    }

    var property details.PropertyInfo
    var err error

    // Get a proxy URL from the rotator
    var proxyURL *url.URL
    if proxyConfig != nil && proxyConfig.ProxyRotator.Count() > 0 {
        proxyURL = proxyConfig.ProxyRotator.GetNext()
        log.Printf("Using proxy for property request: %v", proxyURL)
    }

    // Check for ID parameter
    propertyIDStr := r.URL.Query().Get("id")
    if propertyIDStr != "" {
        propertyID, err := strconv.ParseInt(propertyIDStr, 10, 64)
        if err != nil {
            http.Error(w, fmt.Sprintf("Error parsing property ID: %v", err), http.StatusBadRequest)
            return
        }

        property, err = details.FromPropertyID(propertyID, proxyURL)
    } else {
        // Check for URL parameter
        propertyURL := r.URL.Query().Get("url")
        if propertyURL != "" {
            property, err = details.FromPropertyURL(propertyURL, proxyURL)
        } else {
            // Check for Address parameter
            homeAddress := r.URL.Query().Get("address")
            if homeAddress != "" {
                property, err = details.FromHomeAddress(homeAddress, proxyURL)
            } else {
                // If no valid parameters are provided, return an error
                http.Error(w, "Must provide ID, URL, or Address parameter", http.StatusBadRequest)
                return
            }
        }
    }

    if err != nil {
        http.Error(w, fmt.Sprintf("Error retrieving property details: %v", err), http.StatusInternalServerError)
        return
    }

    if !listingPhotos {
        property.ResponsivePhotos = nil
    }

    writeJSONResponse(w, property)
}

func rentZestimateHandler(w http.ResponseWriter, r *http.Request) {
    address := r.URL.Query().Get("address")
    if address == "" {
        http.Error(w, "Address parameter is required", http.StatusBadRequest)
        return
    }

    var compPropStatus *bool
    compPropStatusStr := r.URL.Query().Get("compPropStatus")
    if compPropStatusStr != "" {
        if compPropStatusStr == "true" || compPropStatusStr == "false" {
            val, _ := strconv.ParseBool(compPropStatusStr)
            compPropStatus = &val
        } else {
            http.Error(w, "Invalid compPropStatus parameter", http.StatusBadRequest)
            return
        }
    }

    distanceInMilesStr := r.URL.Query().Get("distanceInMiles")
    var distanceInMiles float64 = 5 // Default value
    if distanceInMilesStr != "" {
        var err error
        distanceInMiles, err = strconv.ParseFloat(distanceInMilesStr, 64)
        if err != nil {
            http.Error(w, "Invalid distanceInMiles parameter", http.StatusBadRequest)
            return
        }
    }

    // Use the global proxy rotator with retry logic
    rentZestimate, err := zestimate.GetRentZestimateWithRotator(address, compPropStatus, distanceInMiles, globalProxyRotator)
    if err != nil {
        http.Error(w, fmt.Sprintf("Error retrieving rent zestimate: %v", err), http.StatusInternalServerError)
        return
    }

    writeJSONResponse(w, rentZestimate)
}

func main() {
    // Initialize proxy configuration
    var err error
    proxyConfig, err = config.LoadProxyConfig()
    if err != nil {
        log.Fatalf("Failed to load proxy configuration: %v", err)
    }

    globalProxyRotator = proxyConfig.ProxyRotator

    if globalProxyRotator.Count() > 0 {
        log.Printf("Loaded %d proxy URLs for rotation", globalProxyRotator.Count())
    } else {
        log.Println("No proxy URLs configured - requests will be made directly")
    }

    mux := http.NewServeMux()

    mux.HandleFunc("/property", propertyHandler) // New unified endpoint
    mux.HandleFunc("/propertyMinimal", propertyMinimalHandler)
    mux.HandleFunc("/propertyImages", propertyImagesHandler)
    mux.HandleFunc("/rentEstimate", rentZestimateHandler) // New endpoint for property images
    mux.HandleFunc("/status", statusHandler) // Status endpoint to check proxy health
    mux.HandleFunc("/test-proxy", testProxyHandler) // Test proxy connectivity

    // Health Check Endpoint
    mux.HandleFunc("/", healthCheckHandler)

    // Favicon Endpoint
    mux.HandleFunc("/favicon.ico", serveStaticFile("favicon.ico"))

    // Additional static file routes
    mux.HandleFunc("/api-logo.png", serveStaticFile("api-logo.png"))
    mux.HandleFunc("/cover-photo-tutorial-one.png", serveStaticFile("cover-photo-tutorial-one.png"))
    mux.HandleFunc("/rapid/tutorial-one-code", serveStaticFile("tutorials-one.html"))
    mux.HandleFunc("/byte-media-logo-v2.png", serveStaticFile("byte-media-logo-v2.png"))
    mux.HandleFunc("/rapid/curl", serveStaticFile("curl_input.html"))

    // Serve static files from the 'public' directory without authentication
    mux.Handle("/public/", http.StripPrefix("/public/", http.FileServer(http.Dir("public"))))

    // Apply basic request logging middleware (authentication removed)
    handler := requestLoggingMiddleware(mux)

    port := os.Getenv("PORT")
    if port == "" {
        log.Fatal("PORT environment variable is not set")
    }

    fmt.Printf("Server started at port %s (no authentication required)\n", port)
    log.Fatal(http.ListenAndServe(":"+port, handler))
}

func propertyMinimalHandler(w http.ResponseWriter, r *http.Request) {
    // Parse the listingPhotos parameter
    listingPhotosStr := r.URL.Query().Get("listingPhotos")
    listingPhotos := false
    if listingPhotosStr == "true" {
        listingPhotos = true
    }

    // Get a proxy URL from the rotator
    var proxyURL *url.URL
    if proxyConfig != nil && proxyConfig.ProxyRotator.Count() > 0 {
        proxyURL = proxyConfig.ProxyRotator.GetNext()
        log.Printf("Using proxy for property minimal request: %v", proxyURL)
    }

    propertyIDStr := r.URL.Query().Get("id")
    propertyURLParam := r.URL.Query().Get("url")
    homeAddress := r.URL.Query().Get("address")

    var property details.PropertyMinimalInfo
    var err error

    if propertyIDStr != "" {
        propertyID, err := strconv.ParseInt(propertyIDStr, 10, 64)
        if err != nil {
            http.Error(w, fmt.Sprintf("Error parsing property ID: %v", err), http.StatusBadRequest)
            return
        }
        property, err = details.FromPropertyIDMinimal(propertyID, proxyURL)
    } else if propertyURLParam != "" {
        property, err = details.FromPropertyURLMinimal(propertyURLParam, proxyURL)
    } else if homeAddress != "" {
        property, err = details.FromHomeAddressMinimal(homeAddress, proxyURL)
    } else {
        http.Error(w, "Either Property ID, URL, or Address is required", http.StatusBadRequest)
        return
    }

    if err != nil {
        http.Error(w, fmt.Sprintf("Error retrieving property details: %v", err), http.StatusInternalServerError)
        return
    }

    if !listingPhotos {
        property.ResponsivePhotos = nil
    }

    writeJSONResponse(w, property)
}

func propertyImagesHandler(w http.ResponseWriter, r *http.Request) {
    // Get a proxy URL from the rotator
    var proxyURL *url.URL
    if proxyConfig != nil && proxyConfig.ProxyRotator.Count() > 0 {
        proxyURL = proxyConfig.ProxyRotator.GetNext()
        log.Printf("Using proxy for property images request: %v", proxyURL)
    }

    propertyIDStr := r.URL.Query().Get("id")
    propertyURLParam := r.URL.Query().Get("url")
    homeAddress := r.URL.Query().Get("address")

    var property details.ImagesOnly
    var err error

    switch {
    case propertyIDStr != "":
        propertyID, err := strconv.ParseInt(propertyIDStr, 10, 64)
        if err != nil {
            http.Error(w, fmt.Sprintf("Error parsing property ID: %v", err), http.StatusBadRequest)
            return
        }
        property, err = details.FromPropertyIDPhotos(propertyID, proxyURL)
    case propertyURLParam != "":
        property, err = details.FromPropertyURLPhotos(propertyURLParam, proxyURL)
    case homeAddress != "":
        property, err = details.FromHomeAddressPhotos(homeAddress, proxyURL)
    default:
        http.Error(w, "Must provide ID, URL, or Address parameter", http.StatusBadRequest)
        return
    }

    if err != nil {
        http.Error(w, fmt.Sprintf("Error retrieving property details: %v", err), http.StatusInternalServerError)
        return
    }

    writeJSONResponse(w, property)
}


// Status handler to check proxy health and system status
func statusHandler(w http.ResponseWriter, r *http.Request) {
    status := map[string]interface{}{
        "status": "ok",
        "timestamp": time.Now().UTC().Format(time.RFC3339),
        "proxy_count": 0,
        "proxy_status": "disabled",
    }

    if globalProxyRotator != nil {
        status["proxy_count"] = globalProxyRotator.Count()
        if globalProxyRotator.Count() > 0 {
            status["proxy_status"] = "enabled"

            // Test one proxy
            testProxy := globalProxyRotator.GetRandom()
            if testProxy != nil {
                status["sample_proxy"] = testProxy.String()
            }
        }
    }

    writeJSONResponse(w, status)
}

// Test proxy handler to verify proxy connectivity
func testProxyHandler(w http.ResponseWriter, r *http.Request) {
    results := map[string]interface{}{
        "timestamp": time.Now().UTC().Format(time.RFC3339),
        "proxy_tests": []map[string]interface{}{},
        "summary": map[string]interface{}{
            "total_proxies": 0,
            "working_proxies": 0,
            "failed_proxies": 0,
        },
    }

    if globalProxyRotator == nil || globalProxyRotator.Count() == 0 {
        results["message"] = "No proxies configured"
        writeJSONResponse(w, results)
        return
    }

    // Test each proxy
    totalProxies := globalProxyRotator.Count()
    workingCount := 0

    for i := 0; i < totalProxies; i++ {
        proxy := globalProxyRotator.GetNext()
        if proxy == nil {
            continue
        }

        testResult := map[string]interface{}{
            "proxy": proxy.String(),
            "status": "unknown",
            "response_time": 0,
            "error": nil,
        }

        // Test proxy with httpbin.org
        start := time.Now()
        client := &http.Client{
            Timeout: 10 * time.Second,
            Transport: &http.Transport{
                Proxy: http.ProxyURL(proxy),
            },
        }

        resp, err := client.Get("http://httpbin.org/ip")
        duration := time.Since(start)

        if err != nil {
            testResult["status"] = "failed"
            testResult["error"] = err.Error()
        } else {
            resp.Body.Close()
            if resp.StatusCode == 200 {
                testResult["status"] = "working"
                testResult["response_time"] = duration.Milliseconds()
                workingCount++
            } else {
                testResult["status"] = "failed"
                testResult["error"] = fmt.Sprintf("HTTP %d", resp.StatusCode)
            }
        }

        results["proxy_tests"] = append(results["proxy_tests"].([]map[string]interface{}), testResult)
    }

    results["summary"] = map[string]interface{}{
        "total_proxies": totalProxies,
        "working_proxies": workingCount,
        "failed_proxies": totalProxies - workingCount,
        "success_rate": fmt.Sprintf("%.1f%%", float64(workingCount)/float64(totalProxies)*100),
    }

    writeJSONResponse(w, results)
}

func writeJSONResponse(w http.ResponseWriter, data interface{}) {
    w.Header().Set("Content-Type", "application/json")
    rawJSON, err := json.MarshalIndent(data, "", "  ")
    if err != nil {
        http.Error(w, fmt.Sprintf("Error marshalling property details: %v", err), http.StatusInternalServerError)
        return
    }
    w.Write(rawJSON)
}
